#!/usr/bin/env python3
"""
YQProxy 代理池API调用示例
支持本地和远程代理池服务
"""

import requests
from typing import List, Optional

# ========== 配置参数 ==========
API_KEY = 'yqproxy888'
API_URL = 'https://pool.proxygo.de'


def get_proxy(protocol: str = "http", api_key: Optional[str] = None) -> str:
    """
    获取单个代理
    
    参数:
        protocol: 协议类型 (http/socks4/socks5)
        api_key: API密钥 (可选，如果不提供则使用全局配置)
    
    返回:
        代理字符串 (格式: IP:PORT)
    """
    params = {
        "protocol": protocol,
        "API-KEY": api_key or API_KEY
    }
    
    response = requests.get(f"{API_URL}/random", params=params)
    return response.text.strip()


def get_proxies(count: int = 10, protocol: str = "http", api_key: Optional[str] = None) -> List[str]:
    """
    批量获取代理
    
    参数:
        count: 获取数量 (最大1000)
        protocol: 协议类型 (http/socks4/socks5)
        api_key: API密钥 (可选，如果不提供则使用全局配置)
    
    返回:
        代理列表
    """
    params = {
        "count": count,
        "protocol": protocol,
        "API-KEY": api_key or API_KEY
    }
    
    response = requests.get(f"{API_URL}/batch", params=params)
    
    # 处理返回的代理列表
    proxies = response.text.strip().split('\n')
    return [p for p in proxies if p]  # 过滤空行


if __name__ == "__main__":
    http_proxy = get_proxy("http")
    print(http_proxy)
    # socks5_proxy = get_proxy("socks4")
    # print(socks5_proxy)
    # socks5_proxy = get_proxy("socks5")
    # print(socks5_proxy)
    
    # http_proxies = get_proxies(10, "http")
    # print(http_proxies)
    # socks4_proxies = get_proxies(20, "socks4")
    # print(socks4_proxies)
    # socks5_proxies = get_proxies(20, "socks5")
    # print(socks5_proxies)