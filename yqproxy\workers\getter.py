#!/usr/bin/env python3
"""
代理获取器工作进程
负责从爬虫获取代理并存储到代理池
可独立运行，无调度依赖
"""

import asyncio
import sys
from typing import List, Optional
from collections import defaultdict
from yqproxy.utils.logger import get_logger

from yqproxy.services import create_integrated_services, ProxyManager
from yqproxy.crawlers import __all__ as crawler_classes
from yqproxy.models import Proxy
from yqproxy.settings import (
    PROXY_NUMBER_MIN_VALID,
    PROXY_NUMBER_MAX_INVALID
)


class ProxyGetter:
    """
    代理获取器
    负责从各种爬虫源获取代理并存储到代理池
    """
    
    def __init__(self):
        """初始化代理获取器"""
        self.proxy_manager: Optional[ProxyManager] = None
        self.crawlers = []
        self._initialized = False
        self.logger = get_logger(__name__)
        
    async def initialize(self) -> None:
        """初始化服务依赖"""
        if self._initialized:
            return
            
        # 创建集成服务
        _, _, proxy_manager = await create_integrated_services()
        self.proxy_manager = proxy_manager
        
        # 初始化爬虫
        self.crawlers = [crawler_cls() for crawler_cls in crawler_classes]
        self._initialized = True
    
    @staticmethod
    def normalize_protocol(protocol: str) -> str:
        """标准化协议名称（HTTPS->HTTP）"""
        protocol = protocol.upper()
        return 'HTTP' if protocol == 'HTTPS' else protocol
    
    async def _need_fetch_proxies(self) -> bool:
        """
        检查是否需要获取新代理
        
        执行逻辑：
        1. 如果无效代理数量 > PROXY_NUMBER_MAX_INVALID：自动清理中等质量代理（score在11-75之间）
           - 有效代理定义：满分100分的代理
           - 无效代理 = 总代理数 - 满分代理数
        2. 如果有效代理数量 >= PROXY_NUMBER_MIN_VALID：不获取新代理
        3. 如果有效代理数量 < PROXY_NUMBER_MIN_VALID：需要获取新代理
        
        Returns:
            True: 需要获取新代理
            False: 不需要获取（代理池充足）
        """
        stats = await self.proxy_manager.get_statistics()
        
        # 计算有效代理数（score=100的满分代理）和无效代理数
        # 注意：max_score 只包含满分100分的代理
        valid_count = sum(
            protocol_stats.get('max_score', 0)
            for protocol_stats in stats.get('protocols', {}).values()
        )
        total_count = stats.get('total_proxies', 0)
        invalid_count = total_count - valid_count
        
        # 步骤1：如果无效代理太多，先清理掉中等质量代理（11-75分）
        if invalid_count > PROXY_NUMBER_MAX_INVALID:
            await self.proxy_manager.cleanup_invalid_proxies()
            # 清理后重新获取统计
            stats = await self.proxy_manager.get_statistics()
            valid_count = sum(
                protocol_stats.get('max_score', 0)  # max_score = 满分100分的代理
                for protocol_stats in stats.get('protocols', {}).values()
            )
            self.logger.info(f"清理无效代理后，当前有效代理数: {valid_count}")
        
        # 步骤2：判断是否需要获取新代理
        if valid_count >= PROXY_NUMBER_MIN_VALID:
            self.logger.info(f"当前有效代理数: {valid_count}, 无需获取新代理")
            return False  # 代理充足，不需要获取
        
        return True  # 代理不足，需要获取
    
    async def _fetch_from_crawlers(self) -> List[Proxy]:
        """从所有爬虫并发获取代理"""
        if not self.crawlers:
            return []
        
        # 并发执行所有爬虫
        tasks = [
            asyncio.get_event_loop().run_in_executor(
                None, lambda c=crawler: list(c.crawl())
            )
            for crawler in self.crawlers
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集有效代理并统计
        all_proxies = []
        total_protocols = defaultdict(int)
        
        for crawler, result in zip(self.crawlers, results):
            if isinstance(result, Exception):
                continue
                
            # 过滤有效代理
            valid_proxies = [p for p in result if p and p.validate()]
            if not valid_proxies:
                continue
                
            all_proxies.extend(valid_proxies)
            
            # 统计协议分布
            protocol_count = defaultdict(int)
            for proxy in valid_proxies:
                protocol = self.normalize_protocol(proxy.protocol)
                protocol_count[protocol] += 1
                total_protocols[protocol] += 1
            
            # 输出爬虫统计
            if protocol_count:
                protocol_info = ', '.join(f"{p}:{c}" for p, c in protocol_count.items())
                self.logger.info(f"{crawler.__class__.__name__} 获取: {len(valid_proxies)}个代理({protocol_info})")
        
        # 输出总计
        if all_proxies and total_protocols:
            protocol_info = ', '.join(f"{p}:{c}" for p, c in total_protocols.items())
            self.logger.info(f"爬虫任务完成，共获取 {len(all_proxies)} 个代理({protocol_info})")
        
        return all_proxies

    async def _store_to_pool(self, proxies: List[Proxy]) -> None:
        """将代理存储到代理池"""
        if not proxies:
            return
        
        try:
            # 按协议分组并去重
            protocol_groups = defaultdict(list)
            seen = set()
            
            for proxy in proxies:
                proxy_key = proxy.get_normalized_string()
                if proxy_key not in seen:
                    seen.add(proxy_key)
                    # HTTPS统一存储为HTTP
                    protocol = proxy.protocol.lower()
                    if protocol == 'https':
                        protocol = 'http'
                    protocol_groups[protocol].append(proxy_key)
            
            # 批量存储各协议代理
            results = {}
            for protocol, proxy_strings in protocol_groups.items():
                if proxy_strings:
                    protocol_results = await self.proxy_manager.add_proxies_from_strings(proxy_strings)
                    for proto, count in protocol_results.items():
                        results[proto] = results.get(proto, 0) + count
            
            # 输出存储结果
            total_added = sum(results.values())
            if total_added > 0:
                details = ', '.join(f'{p.upper()}:{c}' for p, c in results.items() if c > 0)
                self.logger.info(f"代理池新增 {total_added} 个代理 ({details})")
            
        except Exception as e:
            self.logger.error(f"存储代理时出错: {e}")
    
    async def run_once(self) -> bool:
        """
        运行一次代理获取任务
        
        Returns:
            是否执行了获取操作
        """
        # 确保已初始化
        await self.initialize()
        
        if not self.proxy_manager:
            raise RuntimeError("ProxyManager未初始化")
        
        try:
            # 检查是否需要获取
            if not await self._need_fetch_proxies():
                return False
            
            # 获取代理
            proxies = await self._fetch_from_crawlers()
            if not proxies:
                self.logger.warning("未获取到任何有效代理")
                return True
            
            # 存储代理
            await self._store_to_pool(proxies)
            return True
            
        except Exception as e:
            self.logger.error(f"执行代理获取任务失败: {e}")
            return False
    
    async def cleanup(self) -> None:
        """清理资源"""
        if self.proxy_manager:
            await self.proxy_manager.close()
            self.proxy_manager = None
            self._initialized = False


async def main():
    """主函数 - 执行一次代理获取任务"""
    getter = ProxyGetter()
    logger = get_logger(__name__)
    
    try:
        logger.info("开始执行代理获取任务...")
        
        executed = await getter.run_once()
        
        if not executed:
            logger.info("代理池状态良好，无需获取新代理")
        
        logger.info("代理获取任务完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止...")
        return 0
    except Exception as e:
        logger.error(f"代理获取器运行失败: {e}")
        return 1
    finally:
        await getter.cleanup()


if __name__ == '__main__':
    # 直接运行文件时执行
    exit_code = asyncio.run(main())
    sys.exit(exit_code)