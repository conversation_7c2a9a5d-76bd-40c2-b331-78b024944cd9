"""
数据模型常量定义
纯常量，无任何依赖
"""
from environs import Env

# 初始化环境变量读取器
env = Env()

class Protocol:
    """代理协议类型常量"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


# 支持的协议列表
SUPPORTED_PROTOCOLS = [
    Protocol.HTTP,
    Protocol.HTTPS,
    Protocol.SOCKS4,
    Protocol.SOCKS5
]

# 安全协议列表
SECURE_PROTOCOLS = [
    Protocol.HTTPS,
    Protocol.SOCKS5
]

# 默认协议
DEFAULT_PROTOCOL = Protocol.HTTP

# 代理评分相关常量
# 支持从环境变量读取，提供默认值
PROXY_SCORE_MAX = env.int('PROXY_SCORE_MAX', 100)      # 代理最高分数（满分）
PROXY_SCORE_MIN = env.int('PROXY_SCORE_MIN', 0)        # 代理最低分数（淘汰线）
PROXY_SCORE_INIT = env.int('PROXY_SCORE_INIT', 10)     # 代理初始分数
PROXY_SCORE_DECREASE = env.int('PROXY_SCORE_DECREASE', 1)  # 测试失败时减少的分数