"""
代理统计模块
负责代理池的统计分析和健康检查
"""

import time
from typing import Dict, Any
from yqproxy.utils import get_logger
from yqproxy.models.constants import PROXY_SCORE_MAX, PROXY_SCORE_MIN, SUPPORTED_PROTOCOLS


class ProxyStatistics:
    """
    代理统计器
    负责统计信息收集、健康检查和报告生成
    """
    
    def __init__(self, proxy_pool):
        """
        初始化代理统计器
        
        Args:
            proxy_pool: 代理池实例
        """
        self.proxy_pool = proxy_pool
        self.logger = get_logger(self.__class__.__name__)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        try:
            # 基础统计
            counts = await self.proxy_pool.count_proxies()
            total_proxies = sum(counts.values())
            
            # 详细统计
            stats = {
                'total_proxies': total_proxies,
                'protocols': {},
                'timestamp': time.time()
            }
            
            for protocol in SUPPORTED_PROTOCOLS:
                count = counts.get(protocol, 0)
                
                # 分数段统计
                max_score = await self.proxy_pool.count_by_score_range(
                    protocol, PROXY_SCORE_MAX, PROXY_SCORE_MAX  # 满分100分
                )
                medium_score = await self.proxy_pool.count_by_score_range(
                    protocol, PROXY_SCORE_MAX // 2, PROXY_SCORE_MAX - 1
                )
                low_score = await self.proxy_pool.count_by_score_range(
                    protocol, PROXY_SCORE_MIN + 1, PROXY_SCORE_MAX // 2 - 1
                )
                
                stats['protocols'][protocol] = {
                    'count': count,
                    'max_score': max_score,  # 满分（100分）代理数量
                    'medium_score': medium_score,
                    'low_score': low_score,
                    'health_ratio': round(max_score / count * 100, 2) if count > 0 else 0.0
                }
            
            return stats
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e), 'timestamp': time.time()}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        try:
            counts = await self.proxy_pool.count_proxies()
            total_proxies = sum(counts.values())
            
            # 检查是否有可用代理
            has_proxies = total_proxies > 0
            
            # 检查各协议是否有满分代理
            healthy_protocols = []
            for protocol in SUPPORTED_PROTOCOLS:
                max_score_count = await self.proxy_pool.count_by_score_range(
                    protocol, PROXY_SCORE_MAX, PROXY_SCORE_MAX
                )
                if max_score_count > 0:
                    healthy_protocols.append(protocol)
            
            return {
                'status': 'healthy' if has_proxies else 'warning',
                'total_proxies': total_proxies,
                'healthy_protocols': healthy_protocols,
                'protocol_distribution': counts,
                'timestamp': time.time()
            }
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': time.time()
            }
    
    async def get_protocol_health(self, protocol: str) -> Dict[str, Any]:
        """
        获取特定协议的健康状态
        
        Args:
            protocol: 协议类型
            
        Returns:
            协议健康状态信息
        """
        try:
            count = await self.proxy_pool.count_protocol(protocol)
            
            if count == 0:
                return {
                    'protocol': protocol,
                    'status': 'empty',
                    'count': 0,
                    'health_ratio': 0.0
                }
            
            max_score = await self.proxy_pool.count_by_score_range(
                protocol, PROXY_SCORE_MAX, PROXY_SCORE_MAX
            )
            
            health_ratio = round(max_score / count * 100, 2)
            
            # 判断健康状态
            if health_ratio >= 50:
                status = 'healthy'
            elif health_ratio >= 20:
                status = 'warning'
            else:
                status = 'critical'
            
            return {
                'protocol': protocol,
                'status': status,
                'count': count,
                'max_score_count': max_score,
                'health_ratio': health_ratio
            }
        except Exception as e:
            self.logger.error(f"获取协议健康状态失败 {protocol}: {e}")
            return {
                'protocol': protocol,
                'status': 'error',
                'error': str(e)
            }
    
    async def get_score_distribution(self, protocol: str = None) -> Dict[str, Any]:
        """
        获取分数分布情况
        
        Args:
            protocol: 协议类型（可选，不指定则统计所有协议）
            
        Returns:
            分数分布信息
        """
        try:
            if protocol:
                protocols = [protocol]
            else:
                protocols = SUPPORTED_PROTOCOLS
            
            distribution = {}
            
            for proto in protocols:
                count = await self.proxy_pool.count_protocol(proto)
                if count == 0:
                    continue
                
                # 定义分数区间
                score_ranges = [
                    (PROXY_SCORE_MAX, PROXY_SCORE_MAX, 'perfect'),
                    (80, PROXY_SCORE_MAX - 1, 'excellent'),
                    (60, 79, 'good'),
                    (40, 59, 'fair'),
                    (20, 39, 'poor'),
                    (PROXY_SCORE_MIN, 19, 'critical')
                ]
                
                proto_distribution = {}
                for min_score, max_score, label in score_ranges:
                    count_in_range = await self.proxy_pool.count_by_score_range(
                        proto, min_score, max_score
                    )
                    if count_in_range > 0:
                        proto_distribution[label] = count_in_range
                
                distribution[proto] = proto_distribution
            
            return {
                'distribution': distribution,
                'timestamp': time.time()
            }
        except Exception as e:
            self.logger.error(f"获取分数分布失败: {e}")
            return {'error': str(e), 'timestamp': time.time()}
    
    async def generate_report(self) -> str:
        """
        生成统计报告
        
        Returns:
            格式化的统计报告字符串
        """
        try:
            stats = await self.get_statistics()
            health = await self.health_check()
            
            report_lines = [
                "=" * 50,
                "代理池统计报告",
                "=" * 50,
                f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}",
                f"健康状态: {health['status'].upper()}",
                f"代理总数: {stats['total_proxies']}",
                ""
            ]
            
            # 各协议统计
            report_lines.append("协议分布:")
            for protocol, protocol_stats in stats.get('protocols', {}).items():
                if protocol_stats['count'] > 0:
                    report_lines.append(
                        f"  {protocol}: {protocol_stats['count']} 个 "
                        f"(健康率: {protocol_stats['health_ratio']}%)"
                    )
                    report_lines.append(
                        f"    满分: {protocol_stats['max_score']}, "
                        f"中分: {protocol_stats['medium_score']}, "
                        f"低分: {protocol_stats['low_score']}"
                    )
            
            report_lines.append("=" * 50)
            
            return "\n".join(report_lines)
            
        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")
            return f"生成报告失败: {str(e)}"