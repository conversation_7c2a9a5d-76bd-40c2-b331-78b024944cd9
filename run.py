#!/usr/bin/env python3
"""YQProxy 主入口 - 支持单进程模式"""
import sys
import os
import argparse
import asyncio
import signal
import time
from typing import Optional

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from yqproxy.scheduler import ProxyScheduler
from yqproxy.workers.getter import ProxyGetter
from yqproxy.workers.tester import ProxyTesterWorker
from yqproxy.utils.logger import get_logger
from yqproxy.settings import (
    SCHEDULE_GETTER_INTERVAL,
    SCHEDULE_TESTER_INTERVAL,
    ENABLE_GETTER,
    ENABLE_TESTER,
    ENABLE_SERVER,
    API_HOST,
    API_PORT,
    DEBUG_MODE
)


logger = get_logger(__name__)


def run_tester_loop(cycle: Optional[int] = None):
    """运行测试器循环"""
    if not ENABLE_TESTER:
        logger.info('代理测试器未启用，退出')
        return
    
    cycle = cycle or SCHEDULE_TESTER_INTERVAL
    logger.info(f'启动代理测试器，间隔: {cycle}秒')
    
    tester = ProxyTesterWorker()
    loop_count = 0
    
    while True:
        try:
            logger.debug(f'测试器循环 {loop_count} 开始...')
            asyncio.run(tester.run_once())
            loop_count += 1
            time.sleep(cycle)
        except KeyboardInterrupt:
            logger.info("测试器进程被用户中断")
            break
        except Exception as e:
            logger.error(f"测试器运行错误: {e}")
            time.sleep(cycle)


def run_getter_loop(cycle: Optional[int] = None):
    """运行获取器循环"""
    if not ENABLE_GETTER:
        logger.info('代理获取器未启用，退出')
        return
    
    cycle = cycle or SCHEDULE_GETTER_INTERVAL
    logger.info(f'启动代理获取器，间隔: {cycle}秒')
    
    getter = ProxyGetter()
    loop_count = 0
    
    while True:
        try:
            logger.debug(f'获取器循环 {loop_count} 开始...')
            asyncio.run(getter.run_once())
            loop_count += 1
            time.sleep(cycle)
        except KeyboardInterrupt:
            logger.info("获取器进程被用户中断")
            break
        except Exception as e:
            logger.error(f"获取器运行错误: {e}")
            time.sleep(cycle)


def run_server():
    """运行API服务器"""
    if not ENABLE_SERVER:
        logger.info('API服务器未启用，退出')
        return
    
    logger.info('启动API服务器...')
    
    try:
        if not DEBUG_MODE:
            # 生产模式
            from yqproxy.settings import APP_PROD_METHOD
            
            if APP_PROD_METHOD == 'gevent':
                try:
                    from gevent.pywsgi import WSGIServer
                    from yqproxy.workers.server import app
                    http_server = WSGIServer((API_HOST, API_PORT), app)
                    logger.info(f"使用Gevent服务器在 {API_HOST}:{API_PORT} 启动")
                    http_server.serve_forever()
                except ImportError:
                    run_uvicorn_server()
            elif APP_PROD_METHOD == 'tornado':
                try:
                    from tornado.wsgi import WSGIContainer
                    from tornado.httpserver import HTTPServer
                    from tornado.ioloop import IOLoop
                    from yqproxy.workers.server import app
                    
                    http_server = HTTPServer(WSGIContainer(app))
                    http_server.listen(API_PORT, address=API_HOST)
                    logger.info(f"使用Tornado服务器在 {API_HOST}:{API_PORT} 启动")
                    IOLoop.instance().start()
                except ImportError:
                    run_uvicorn_server()
            else:
                run_uvicorn_server()
        else:
            # 调试模式
            run_uvicorn_server()
    except KeyboardInterrupt:
        logger.info("服务器进程被用户中断")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        sys.exit(1)


def run_uvicorn_server():
    """使用Uvicorn运行服务器"""
    import uvicorn
    from yqproxy.workers.server import app
    
    logger.info(f"使用Uvicorn服务器在 {API_HOST}:{API_PORT} 启动")
    uvicorn.run(
        app,
        host=API_HOST,
        port=API_PORT,
        workers=1,
        log_level="info" if not DEBUG_MODE else "debug"
    )


def setup_signal_handlers():
    """设置信号处理器（Unix系统）"""
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在退出...")
        sys.exit(0)
    
    if os.name != 'nt':  # 非Windows系统
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='YQProxy - 协议感知代理池',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 运行所有进程（默认）
  python run.py
  
  # 只运行测试器
  python run.py --processor tester
  
  # 只运行获取器
  python run.py --processor getter
  
  # 只运行API服务器
  python run.py --processor server
  
  # 指定运行间隔（秒）
  python run.py --processor tester --cycle 60
"""
    )
    
    parser.add_argument(
        '--processor',
        choices=['tester', 'getter', 'server', 'all'],
        default='all',
        help='要运行的处理器类型（默认: all）'
    )
    
    parser.add_argument(
        '--cycle',
        type=int,
        default=None,
        help='运行周期间隔（秒），仅对tester和getter有效'
    )
    
    args = parser.parse_args()
    
    # 设置信号处理
    setup_signal_handlers()
    
    # 根据参数运行对应的处理器
    if args.processor == 'tester':
        run_tester_loop(args.cycle)
    elif args.processor == 'getter':
        run_getter_loop(args.cycle)
    elif args.processor == 'server':
        run_server()
    else:
        # 运行完整的调度器
        scheduler = ProxyScheduler()
        scheduler.run()


if __name__ == '__main__':
    main()
