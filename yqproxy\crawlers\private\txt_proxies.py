# encoding: utf-8
import re
import time
import random
import urllib3
import concurrent.futures
from retrying import RetryError
from yqproxy.crawlers.base import BaseCrawler
from yqproxy.models import Proxy
from yqproxy.utils import get_logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 每个代理源的最大代理数限制
# 如果源包含太多代理，其中很多可能无效
# 设置为0表示不限制
MAX_PROXIES_PER_SOURCE = 100
# 获取代理源的超时时间（秒）
TIMEOUT = 10.0

# 分协议的代理源URL配置
HTTP_URLS = [
    'https://gist.githubusercontent.com/yyh357/566fc1afc659e42df1186785775991f6/raw/082ae98d2a434ae2b6880209850080df73e9533b/gistfile1.txt',
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=http",
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=https",
    "https://api.openproxylist.xyz/http.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=http",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=https",
    "https://proxyspace.pro/http.txt",
    "https://proxyspace.pro/https.txt",
    "http://pubproxy.com/api/proxy?limit=5&format=txt&type=http&level=anonymous&last_check=60&no_country=CN",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/http/data.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/https/data.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/http_proxies.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/https_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/http_proxies.txt",
    "https://raw.githubusercontent.com/AlestackOverglow/proxy-list/refs/heads/main/proxies_with_protocol.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/andigwandi/free-proxy/refs/heads/main/proxy_list.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/http_proxies.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/https_proxies.txt",
    "https://raw.githubusercontent.com/aslisk/proxyhttps/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/http.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/http_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/http/global/http_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/hendrikbgr/Free-Proxy-Repo/refs/heads/master/proxy_list.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/MrMarble/proxy-list/refs/heads/main/all.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/http_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/http.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/https.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/http/http.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/https/https.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Https.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/HTTPS_RAW.txt",
    "https://raw.githubusercontent.com/saisuiu/Lionkings-Http-Proxys-Proxies/refs/heads/main/free.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/Skiddle-ID/proxylist/refs/heads/main/proxies.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/http_proxies.txt",
    "https://raw.githubusercontent.com/themiralay/Proxy-List-World/refs/heads/master/data.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/http.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/https.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/http_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=http",
    "https://www.proxy-list.download/api/v1/get?type=https",
]

SOCKS4_URLS = [
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=socks4",
    "https://api.openproxylist.xyz/socks4.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=socks4",
    "https://proxyspace.pro/socks4.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS4.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/socks4/data.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks4_proxies.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks4_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/socks4.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/socks4_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/socks4/global/socks4_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/socks4_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/socks4.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/socks4/socks4.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Socks4.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/SOCKS4_RAW.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/socks4_proxies.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/socks4.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/socks4_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=socks4",
]

SOCKS5_URLS = [
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=socks5",
    "https://api.openproxylist.xyz/socks5.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=socks5",
    "https://proxyspace.pro/socks5.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS5.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/socks5/data.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks5_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks5_proxies.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/socks5.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/socks5_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/socks5/global/socks5_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/hookzof/socks5_list/refs/heads/master/proxy.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/socks5_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/socks5.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/socks5/socks5.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Socks5.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/SOCKS5_RAW.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/socks5_proxies.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/socks5.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/socks5_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=socks5",
]


class TXTProxiesCrawler(BaseCrawler):
    """TXT代理列表爬虫 - 支持协议分类"""

    def __init__(self):
        super().__init__()
        self.logger = get_logger(self.__class__.__name__)
        # 使用字典合并方式构建URL和协议的映射
        self.url_protocol_map = {
            **{url: 'http' for url in HTTP_URLS},
            **{url: 'socks4' for url in SOCKS4_URLS},
            **{url: 'socks5' for url in SOCKS5_URLS}
        }
        self.urls = list(self.url_protocol_map.keys())

    
    def crawl(self):
        """并行爬取"""
        def fetch_and_process(url):
            """获取并处理单个URL的代理"""
            # 针对GitHub链接增加延迟
            if 'github.com' in url or 'githubusercontent.com' in url:
                time.sleep(random.uniform(1.0, 2.0))
            
            try:
                html = self.fetch(url)
                if not html:
                    self.logger.warning(f'URL返回空内容: {url}')
                    return []
                
                return list(self.process(html, url))
            except RetryError:
                self.logger.error(f'URL获取失败: {url}')
                return []
            except Exception as e:
                self.logger.error(f'处理URL出错 {url}: {e}')
                return []
        
        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(fetch_and_process, url) for url in self.urls]
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    proxies = future.result()
                    yield from proxies
                except Exception as e:
                    self.logger.error(f'处理结果出错: {e}')

    def process(self, html, url):
        """处理HTML内容，提取代理"""
        if not html or not url:
            return
        
        # 根据URL确定协议
        protocol = self.url_protocol_map.get(url, 'http')
        
        # 先统计该URL中的代理总数
        lines = [line.strip() for line in html.strip().split('\n') 
                if line.strip() and not line.strip().startswith('#')]
        
        total_lines = len(lines)
        
        # 如果设置了MAX_PROXIES_PER_SOURCE限制且该URL代理数量超过限制，则跳过整个URL
        if MAX_PROXIES_PER_SOURCE > 0 and total_lines > MAX_PROXIES_PER_SOURCE:
            self.logger.info(f'跳过URL {url}，代理数量 {total_lines} 超过限制 {MAX_PROXIES_PER_SOURCE}')
            return
        
        proxy_count = 0
        # 处理代理
        for line in lines:
            try:
                # 清理行内容，移除可能的额外信息（如国家信息）
                cleaned_line = self._clean_proxy_line(line)
                if not cleaned_line:
                    continue
                
                # 如果已经有协议前缀，直接解析
                if cleaned_line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
                    proxy = Proxy.from_string(cleaned_line)
                else:
                    # 纯ip:port格式，根据URL来源添加协议前缀
                    proxy_with_protocol = f'{protocol}://{cleaned_line}'
                    proxy = Proxy.from_string(proxy_with_protocol)
                
                if proxy and proxy.is_valid():
                    proxy_count += 1
                    yield proxy
                    
            except Exception as e:
                self.logger.debug(f'解析代理失败: {line} - {e}')
        
        self.logger.info(f'从 {url} 获取到 {proxy_count} 个代理')

    def _clean_proxy_line(self, line):
        """清理代理行，使用正则表达式提取IP:PORT"""
        if not line:
            return None
        
        line = line.strip()
        
        # 跳过注释行和空行
        if not line or line.startswith(('#', '//')):
            return None
        
        # 使用正则表达式提取IP:PORT
        ip_port_pattern = re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}:\d{1,5}\b')
        match = ip_port_pattern.search(line)
        
        if match:
            ip_port = match.group(0)
            # 简单验证端口范围
            try:
                _, port_str = ip_port.split(':')
                port = int(port_str)
                if 1 <= port <= 65535:
                    return ip_port
            except:
                pass
        
        # 对于带协议前缀的格式，返回完整URL
        if line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
            if ip_port_pattern.search(line):
                return line
        
        return None

