import time

import requests
from fake_headers import Headers
from retrying import RetryError, retry
from yqproxy.utils import get_logger


class BaseCrawler(object):
    urls = []
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

    @retry(stop_max_attempt_number=3, retry_on_result=lambda x: x is None, wait_fixed=2000)
    def fetch(self, url, timeout=5.0, **kwargs):
        """
        Fetch URL with 429 rate limit handling
        """
        return self._fetch_with_429_handling(url, timeout, **kwargs)
    
    def _fetch_with_429_handling(self, url, timeout=10.0, **kwargs):
        """
        Internal fetch method with 429 status code handling
        """
        # 429重试延迟：5秒、10秒、20秒
        retry_delays = [5, 10, 20]
        
        for attempt in range(4):  # 总共4次尝试（1次初始 + 3次重试）
            try:
                headers = Headers(headers=True).generate()
                
                # 增强反爬虫对策
                headers.update({
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                })
                
                kwargs.setdefault('timeout', timeout)
                kwargs.setdefault('verify', False)
                kwargs.setdefault('headers', headers)
                kwargs.setdefault('allow_redirects', True)
                
                response = requests.get(url, **kwargs)
                
                if response.status_code == 200:
                    response.encoding = 'utf-8'
                    content = response.text.strip()
                    if not content:
                        self.logger.warning(f'URL returned empty content (200 OK but no data): {url}')
                        return None
                    return content
                elif response.status_code == 429:
                    # 429状态码处理
                    if attempt < 3:  # 还有重试机会
                        delay = retry_delays[attempt]
                        self.logger.warning(f'Received 429 rate limit for {url}, waiting {delay}s before retry {attempt + 1}/3')
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f'Max 429 retries exceeded for {url}')
                        return None
                else:
                    # 其他状态码记录详细信息
                    self.logger.warning(f'HTTP {response.status_code} for {url} - Content length: {len(response.text)} - Headers: {dict(response.headers)}')
                    return None
                    
            except requests.ConnectionError as e:
                self.logger.warning(f'Connection error for {url}: {e}')
                return None
            except requests.ReadTimeout as e:
                self.logger.warning(f'Read timeout for {url}: {e}')
                return None
            except Exception as e:
                self.logger.error(f'Unexpected error for {url}: {e}')
                return None
        
        return None

    def process(self, html, url):
        """
        used for parse html
        """
        for proxy in self.parse(html):
            self.logger.info(f'fetched proxy {proxy.address} from {url}')
            yield proxy

    def crawl(self):
        """
        crawl main method
        """
        try:
            for url in self.urls:
                self.logger.debug(f'fetching {url}')
                html = self.fetch(url)
                if not html:
                    continue
                time.sleep(.5)
                yield from self.process(html, url)
        except RetryError:
            self.logger.error(
                f'crawler {self} crawled proxy unsuccessfully, '
                'please check if target url is valid or network issue')
