#!/usr/bin/env python3
"""
代理测试器工作进程
基于services层重构的代理测试逻辑，独立可运行
"""

import asyncio
import sys
from typing import Dict, List, Tuple
from tqdm import tqdm

from yqproxy.services import TestConfig, create_integrated_services
from yqproxy.models import SUPPORTED_PROTOCOLS
from yqproxy.utils.logger import get_logger
from yqproxy.settings import (
    TESTER_BATCH_SIZE,
    TESTER_MAX_CONCURRENT,
    TEST_TIMEOUT,
    TEST_URL,
    TEST_URL_MODE,
    TEST_DONT_SET_MAX_SCORE
)
from yqproxy.models.constants import PROXY_SCORE_DECREASE


class ProxyTesterWorker:
    """代理测试器工作进程"""
    
    def __init__(self):
        self.proxy_manager = None
        self.proxy_tester = None
        self.running = False
        self.test_stats = {}
        self.logger = get_logger(__name__)
        
    async def initialize(self):
        """初始化服务依赖"""
        test_config = TestConfig(
            test_url_mode=TEST_URL_MODE,
            test_url=TEST_URL,
            timeout=TEST_TIMEOUT,
            valid_status_codes=[200, 302]
        )
        
        # 使用关键字参数正确传递 test_config
        _, self.proxy_tester, self.proxy_manager = await create_integrated_services(
            redis_client=None,  # 使用默认 Redis 客户端
            test_config=test_config
        )
        
        self.logger.info(
            f"代理测试器初始化完成，配置："
            f"批量{TESTER_BATCH_SIZE}个，并发{TESTER_MAX_CONCURRENT}个"
        )
    
    async def test_single_proxy(self, proxy) -> bool:
        """测试单个代理并更新分数"""
        try:
            result = await self.proxy_tester.test_single_proxy(proxy)
            
            if result.success:
                if not TEST_DONT_SET_MAX_SCORE:
                    await self.proxy_manager.set_proxy_max_score(proxy)
                return True
            else:
                await self.proxy_manager.decrease_proxy_score(proxy, PROXY_SCORE_DECREASE)
                return False
        except Exception as e:
            self.logger.debug(f"测试代理失败: {proxy.address} -> {e}")
            await self.proxy_manager.decrease_proxy_score(proxy, PROXY_SCORE_DECREASE)
            return False
    
    async def test_batch(self, proxies: List) -> Tuple[int, int]:
        """测试一批代理，返回(成功数, 失败数)"""
        semaphore = asyncio.Semaphore(TESTER_MAX_CONCURRENT)
        
        async def test_with_limit(proxy):
            async with semaphore:
                return await self.test_single_proxy(proxy)
        
        results = await asyncio.gather(*[test_with_limit(p) for p in proxies], return_exceptions=True)
        success = sum(1 for r in results if isinstance(r, bool) and r)
        return success, len(proxies) - success
    
    async def test_protocol(self, protocol: str, pbar: tqdm = None) -> Dict:
        """测试单个协议的所有代理"""
        proxies = await self.proxy_manager.proxy_pool.get_all_proxies(protocol)
        if not proxies:
            return {'success': 0, 'failed': 0, 'total': 0}
        
        success_total = failed_total = 0
        
        # 分批测试
        for i in range(0, len(proxies), TESTER_BATCH_SIZE):
            if not self.running:
                break
                
            batch = proxies[i:i + TESTER_BATCH_SIZE]
            success, failed = await self.test_batch(batch)
            
            success_total += success
            failed_total += failed
            
            if pbar:
                pbar.update(len(batch))
                pbar.set_postfix({'success': success_total, 'failed': failed_total})
        
        return {
            'success': success_total,
            'failed': failed_total,
            'total': success_total + failed_total
        }
    
    async def get_active_protocols(self) -> List[Tuple[str, int]]:
        """获取有代理的协议列表，返回 [(protocol, count), ...]"""
        stats = await self.proxy_manager.get_statistics()
        protocols = []
        
        for protocol in SUPPORTED_PROTOCOLS:
            count = stats.get('protocols', {}).get(protocol, {}).get('count', 0)
            if count > 0:
                protocols.append((protocol, count))
        
        return protocols
    
    async def run_test_cycle(self):
        """运行一轮测试"""
        self.running = True
        
        # 获取活动协议
        protocols = await self.get_active_protocols()
        if not protocols:
            self.logger.info('没有代理需要测试')
            return
        
        self.logger.info(f'当前测试模式：{TEST_URL_MODE}，测试URL：{TEST_URL}')
        print(f'{"="*20} 当前测试模式：{TEST_URL_MODE} {"="*20}\n')
        
        # 创建进度条
        progress_bars = {
            protocol: tqdm(
                total=count,
                desc=f'{protocol.upper():>7}',
                unit='代理',
                position=idx,
                leave=True,
                ncols=100
            )
            for idx, (protocol, count) in enumerate(protocols)
        }
        
        # 并行测试所有协议
        tasks = [
            self.test_protocol(protocol, progress_bars[protocol])
            for protocol, _ in protocols
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新统计
        for (protocol, _), result in zip(protocols, results):
            if isinstance(result, dict):
                self.test_stats[protocol] = result
        
        # 清理进度条
        for pbar in progress_bars.values():
            pbar.close()
        print('\n' * len(progress_bars))
        
        # 输出总结
        total_success = sum(s.get('success', 0) for s in self.test_stats.values())
        total_tested = sum(s.get('total', 0) for s in self.test_stats.values())
        success_rate = round(total_success / total_tested * 100, 2) if total_tested > 0 else 0
        
        self.logger.info(f'测试完成: 总共 {total_tested} 个，成功 {total_success} 个，成功率 {success_rate}%')
    
    async def run_once(self):
        """
        运行一次代理测试任务（与 ProxyGetter 保持一致的接口）
        
        Returns:
            bool: 是否成功执行测试
        """
        # 确保已初始化
        if not self.proxy_manager:
            await self.initialize()
        
        try:
            self.test_stats = {}
            await self.run_test_cycle()
            return True
        except Exception as e:
            self.logger.error(f"执行代理测试任务失败: {e}")
            return False
    
    async def run(self):
        """运行一次代理测试任务"""
        await self.initialize()
        try:
            self.test_stats = {}
            await self.run_test_cycle()
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        self.running = False
        if self.proxy_manager:
            await self.proxy_manager.close()
        self.logger.info("代理测试器资源清理完成")


async def main():
    """主函数"""
    tester = ProxyTesterWorker()
    logger = get_logger(__name__)
    
    try:
        await tester.run()
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止...")
        await tester.cleanup()
    except Exception as e:
        logger.error(f"代理测试器运行失败: {e}")
        await tester.cleanup()
        sys.exit(1)


if __name__ == '__main__':
    # 使用项目统一的日志配置
    asyncio.run(main())