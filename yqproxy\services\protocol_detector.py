"""
协议检测服务
纯函数式协议检测和解析服务，无状态，只依赖models层
"""

import re
from functools import lru_cache
from typing import Optional, Tuple

# 只依赖models层
from yqproxy.models import Protocol, SUPPORTED_PROTOCOLS, DEFAULT_PROTOCOL


class ProtocolDetector:
    """
    协议检测服务
    提供纯函数式的协议检测、解析和标准化功能
    """
    
    # 预编译正则表达式（性能优化）
    _PROTOCOL_PATTERN = re.compile(r'^(https?|socks[45])://', re.IGNORECASE)
    _HOST_PORT_PATTERN = re.compile(r'^([^:]+):(\d+)$')
    _AUTH_PATTERN = re.compile(r'^([^:]+):([^@]+)@(.+)$')
    
    # URL类型到协议的映射
    _URL_TYPE_TO_PROTOCOL = {
        'http_urls': Protocol.HTTP,
        'socks4_urls': Protocol.SOCKS4,
        'socks5_urls': Protocol.SOCKS5,
    }

    @classmethod
    @lru_cache(maxsize=1024)
    def detect(cls, proxy_string: str) -> Optional[str]:
        """
        检测代理协议（简化接口）
        
        Args:
            proxy_string: 代理字符串
            
        Returns:
            协议类型字符串或None
        """
        return cls.detect_protocol_from_string(proxy_string)
    
    @classmethod
    @lru_cache(maxsize=1024)
    def detect_protocol_from_string(cls, proxy_string: str) -> Optional[str]:
        """
        从代理字符串检测协议类型
        
        Args:
            proxy_string: 代理字符串
            
        Returns:
            协议类型字符串或None
        """
        if not proxy_string:
            return None
            
        proxy_string = proxy_string.strip()
        
        # 检查协议前缀
        match = cls._PROTOCOL_PATTERN.match(proxy_string)
        if match:
            protocol_str = match.group(1).lower()
            if protocol_str in SUPPORTED_PROTOCOLS:
                return protocol_str
        
        return None

    @classmethod
    def infer_protocol_from_source(cls, url_type: str) -> Optional[str]:
        """
        根据来源URL类型推断协议
        
        Args:
            url_type: URL类型 ('http_urls', 'socks4_urls', 'socks5_urls')
            
        Returns:
            推断的协议类型或None
        """
        return cls._URL_TYPE_TO_PROTOCOL.get(url_type)

    @classmethod
    @lru_cache(maxsize=512)
    def normalize_proxy_string(cls, proxy_string: str) -> str:
        """
        标准化代理字符串，移除协议前缀
        
        Args:
            proxy_string: 原始代理字符串
            
        Returns:
            标准化后的host:port格式
        """
        if not proxy_string:
            return proxy_string
            
        proxy_string = proxy_string.strip()
        
        # 移除协议前缀
        if '://' in proxy_string:
            proxy_string = proxy_string.split('://', 1)[1]
        
        return proxy_string

    @classmethod
    def parse_proxy_components(cls, proxy_string: str) -> Tuple[Optional[str], Optional[int], Optional[str], Optional[str]]:
        """
        解析代理字符串的各个组件
        
        Args:
            proxy_string: 代理字符串
            
        Returns:
            (host, port, username, password) 元组
        """
        if not proxy_string:
            return None, None, None, None
        
        # 标准化字符串
        normalized = cls.normalize_proxy_string(proxy_string)
        
        username = None
        password = None
        
        # 检查认证信息
        if '@' in normalized:
            auth_match = cls._AUTH_PATTERN.match(normalized)
            if auth_match:
                username = auth_match.group(1)
                password = auth_match.group(2)
                host_port = auth_match.group(3)
            else:
                # 简单分割方式作为后备
                auth_part, host_port = normalized.rsplit('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
                else:
                    username = auth_part
        else:
            host_port = normalized
        
        # 解析host和port
        host_port_match = cls._HOST_PORT_PATTERN.match(host_port)
        if host_port_match:
            host = host_port_match.group(1)
            try:
                port = int(host_port_match.group(2))
                # 验证端口范围
                if not (1 <= port <= 65535):
                    return None, None, None, None
            except ValueError:
                return None, None, None, None
        else:
            return None, None, None, None
        
        return host, port, username, password

    @classmethod
    def validate_protocol(cls, protocol: str) -> bool:
        """
        验证协议是否支持
        
        Args:
            protocol: 协议字符串
            
        Returns:
            是否为支持的协议
        """
        return protocol in SUPPORTED_PROTOCOLS

    @classmethod
    def standardize_for_usage(cls, host: str, port: int, protocol: str, 
                            username: Optional[str] = None, password: Optional[str] = None) -> str:
        """
        为使用标准化代理字符串
        
        Args:
            host: 主机地址
            port: 端口
            protocol: 协议类型
            username: 用户名（可选）
            password: 密码（可选）
            
        Returns:
            完整的代理URL
        """
        if username and password:
            return f"{protocol}://{username}:{password}@{host}:{port}"
        elif username:
            return f"{protocol}://{username}@{host}:{port}"
        else:
            return f"{protocol}://{host}:{port}"

    @classmethod
    def parse_proxy_string(cls, proxy_string: str, default_protocol: str = DEFAULT_PROTOCOL) -> Optional[dict]:
        """
        解析代理字符串（简化接口）
        
        Args:
            proxy_string: 代理字符串
            default_protocol: 默认协议
            
        Returns:
            包含解析信息的字典或None
        """
        return cls.detect_and_parse(proxy_string, default_protocol)
    
    @classmethod
    def detect_and_parse(cls, proxy_string: str, default_protocol: str = DEFAULT_PROTOCOL) -> Optional[dict]:
        """
        检测协议并解析代理字符串的完整信息
        
        Args:
            proxy_string: 代理字符串
            default_protocol: 默认协议
            
        Returns:
            包含解析信息的字典或None
        """
        if not proxy_string:
            return None
        
        # 检测协议
        protocol = cls.detect_protocol_from_string(proxy_string)
        if not protocol:
            protocol = default_protocol
        
        # 解析组件
        host, port, username, password = cls.parse_proxy_components(proxy_string)
        if not host or not port:
            return None
        
        return {
            'host': host,
            'port': port,
            'protocol': protocol,
            'username': username,
            'password': password,
            'address': f"{host}:{port}",
            'url': cls.standardize_for_usage(host, port, protocol, username, password)
        }

    @classmethod
    def get_protocol_priority(cls) -> list:
        """
        获取协议优先级列表
        
        Returns:
            协议优先级列表
        """
        return [
            Protocol.HTTP,
            Protocol.SOCKS5,
            Protocol.SOCKS4
        ]

    @classmethod
    def is_secure_protocol(cls, protocol: str) -> bool:
        """
        检查协议是否为安全协议
        
        Args:
            protocol: 协议字符串
            
        Returns:
            是否为安全协议
        """
        from yqproxy.models import SECURE_PROTOCOLS
        return protocol in SECURE_PROTOCOLS

    @classmethod
    def get_redis_key_for_protocol(cls, protocol: str, base_key: str = "proxies") -> str:
        """
        根据协议类型生成Redis键
        
        Args:
            protocol: 协议类型
            base_key: 基础键名
            
        Returns:
            协议特定的Redis键
        """
        return f"{base_key}:{protocol}"

    @classmethod
    def get_all_protocol_keys(cls, base_key: str = "proxies") -> dict:
        """
        获取所有协议对应的Redis键
        
        Args:
            base_key: 基础键名
            
        Returns:
            协议到Redis键的映射 {protocol: key}
        """
        return {
            protocol: cls.get_redis_key_for_protocol(protocol, base_key)
            for protocol in SUPPORTED_PROTOCOLS
        }