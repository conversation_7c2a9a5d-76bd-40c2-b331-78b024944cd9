"""
代理解析器模块
负责代理字符串的解析和转换
"""

from typing import Dict, List, Optional
from yqproxy.utils import get_logger
from yqproxy.models import Proxy, Protocol


class ProxyParser:
    """
    代理解析器
    负责从字符串解析代理信息
    """
    
    def __init__(self, protocol_detector=None):
        """
        初始化代理解析器
        
        Args:
            protocol_detector: 协议检测器（可选）
        """
        self._protocol_detector = protocol_detector
        self.logger = get_logger(self.__class__.__name__)
    
    def set_protocol_detector(self, detector):
        """设置协议检测器"""
        self._protocol_detector = detector
    
    def parse_proxy_string(self, proxy_string: str, 
                          default_protocol: str = Protocol.HTTP) -> Optional[Proxy]:
        """
        从字符串解析单个代理
        
        Args:
            proxy_string: 代理字符串
            default_protocol: 默认协议
            
        Returns:
            代理对象，解析失败返回 None
        """
        # 使用协议检测器解析（如果可用）
        if self._protocol_detector:
            parsed = self._protocol_detector.detect_and_parse(proxy_string, default_protocol)
            if parsed:
                proxy = Proxy(
                    host=parsed['host'],
                    port=parsed['port'],
                    protocol=parsed['protocol'],
                    username=parsed.get('username'),
                    password=parsed.get('password')
                )
                return proxy
        
        # 后备方案：使用Proxy.from_string
        proxy = Proxy.from_string(proxy_string, default_protocol)
        if not proxy:
            self.logger.warning(f"无法解析代理字符串: {proxy_string}")
        
        return proxy
    
    def parse_proxy_strings(self, proxy_strings: List[str],
                           default_protocol: str = Protocol.HTTP) -> List[Proxy]:
        """
        从字符串列表批量解析代理
        
        Args:
            proxy_strings: 代理字符串列表
            default_protocol: 默认协议
            
        Returns:
            成功解析的代理列表
        """
        proxies = []
        failed_count = 0
        
        for proxy_string in proxy_strings:
            proxy = self.parse_proxy_string(proxy_string, default_protocol)
            if proxy:
                proxies.append(proxy)
            else:
                failed_count += 1
        
        if failed_count > 0:
            self.logger.warning(f"解析失败的代理数量: {failed_count}")
        
        return proxies
    
    def parse_with_batch_protocol_detection(self, proxy_strings: List[str],
                                           default_protocol: str = Protocol.HTTP) -> Dict[str, List[Proxy]]:
        """
        批量解析并按协议分组
        
        Args:
            proxy_strings: 代理字符串列表
            default_protocol: 默认协议
            
        Returns:
            按协议分组的代理字典 {protocol: [proxies]}
        """
        protocol_groups = {}
        failed_count = 0
        
        for proxy_string in proxy_strings:
            proxy = self.parse_proxy_string(proxy_string, default_protocol)
            if proxy:
                protocol = proxy.protocol
                if protocol not in protocol_groups:
                    protocol_groups[protocol] = []
                protocol_groups[protocol].append(proxy)
            else:
                failed_count += 1
        
        if failed_count > 0:
            self.logger.warning(f"批量解析失败的代理数量: {failed_count}")
        
        # 记录解析结果
        for protocol, proxies in protocol_groups.items():
            self.logger.info(f"协议 {protocol}: 解析成功 {len(proxies)} 个代理")
        
        return protocol_groups
    
    def validate_proxy_format(self, proxy_string: str) -> bool:
        """
        验证代理字符串格式是否有效
        
        Args:
            proxy_string: 代理字符串
            
        Returns:
            格式是否有效
        """
        try:
            # 尝试解析来验证格式
            proxy = self.parse_proxy_string(proxy_string)
            return proxy is not None
        except Exception as e:
            self.logger.debug(f"代理格式验证失败: {proxy_string} -> {e}")
            return False