"""
纯Redis客户端
只依赖models层，提供基础Redis操作，无业务逻辑
"""

from typing import Dict, List, Optional, Tuple, Any
import redis.asyncio as redis

# 只依赖models层
# 从settings导入Redis配置
from yqproxy.settings import (
    REDIS_HOST,
    REDIS_PORT,
    REDIS_PASSWORD,
    REDIS_DB,
    REDIS_CONNECTION_STRING
)


class RedisClient:
    """
    纯Redis客户端，只负责基础Redis操作
    不包含任何业务逻辑和协议处理
    """
    
    def __init__(self,
                 host: str = None,
                 port: int = None,
                 password: str = None,
                 db: int = None,
                 url: str = None,
                 max_connections: int = 100):
        """
        初始化Redis客户端
        
        Args:
            host: Redis主机地址
            port: Redis端口
            password: Redis密码
            db: Redis数据库编号
            url: Redis连接URL（优先使用）
            max_connections: 连接池最大连接数
        """
        self._client = None
        
        # 从settings获取默认配置
        self._host = host or REDIS_HOST
        self._port = port or REDIS_PORT
        self._password = password or REDIS_PASSWORD
        self._db = db or REDIS_DB
        self._max_connections = max_connections
        
        # 如果提供了URL，优先使用URL；否则构建URL
        if url:
            self._url = url
        elif REDIS_CONNECTION_STRING:
            self._url = REDIS_CONNECTION_STRING
        else:
            # 根据配置构建Redis URL
            if self._password:
                self._url = f"redis://:{self._password}@{self._host}:{self._port}/{self._db}"
            else:
                self._url = f"redis://{self._host}:{self._port}/{self._db}"
        
        # 连接池配置 - 增加超时时间
        self._pool_config = {
            'max_connections': self._max_connections,
            'decode_responses': True,
            'socket_connect_timeout': 10,  # 增加连接超时时间
            'socket_timeout': 15,          # 增加读写超时时间
            'retry_on_timeout': True,
            'retry_on_error': [ConnectionError, TimeoutError],
        }
    
    async def get_connection(self) -> redis.Redis:
        """
        获取Redis连接（懒加载）
        
        Returns:
            Redis连接实例
        """
        if self._client is None:
            try:
                if self._url:
                    self._client = redis.from_url(
                        self._url,
                        **self._pool_config
                    )
                else:
                    self._client = redis.Redis(
                        host=self._host,
                        port=self._port,
                        password=self._password,
                        db=self._db,
                        **self._pool_config
                    )
                
                # 测试连接
                await self._client.ping()
                
            except Exception as e:
                self.logger.error(f"Redis连接失败: {e}")
                raise
        
        return self._client
    
    async def ping(self) -> bool:
        """
        测试Redis连接
        
        Returns:
            连接是否正常
        """
        try:
            client = await self.get_connection()
            result = await client.ping()
            return result is True
        except Exception as e:
            self.logger.error(f"Redis ping失败: {e}")
            return False
    
    async def close(self):
        """关闭Redis连接"""
        if self._client:
            await self._client.close()
            self._client = None
    
    # ==================== 基础Redis操作 ====================
    
    async def zadd(self, key: str, mapping: Dict[str, float], nx: bool = False, xx: bool = False) -> int:
        """
        添加有序集合成员
        
        Args:
            key: Redis键
            mapping: 成员和分数的映射 {member: score}
            nx: 仅当成员不存在时添加
            xx: 仅当成员存在时更新
            
        Returns:
            添加的成员数量
        """
        client = await self.get_connection()
        return await client.zadd(key, mapping, nx=nx, xx=xx)
    
    async def zrangebyscore(self, key: str, min_score: float, max_score: float,
                           start: int = None, num: int = None) -> List[str]:
        """
        按分数范围获取有序集合成员
        
        Args:
            key: Redis键
            min_score: 最小分数
            max_score: 最大分数
            start: 起始位置
            num: 返回数量
            
        Returns:
            成员列表
        """
        client = await self.get_connection()
        # 当start和num都为None时，不传递这两个参数（获取所有成员）
        if start is None and num is None:
            return await client.zrangebyscore(key, min_score, max_score)
        else:
            # 否则必须同时传递start和num
            return await client.zrangebyscore(key, min_score, max_score, start=start, num=num)
    
    async def zrem(self, key: str, *members: str) -> int:
        """
        删除有序集合成员
        
        Args:
            key: Redis键
            members: 要删除的成员
            
        Returns:
            删除的成员数量
        """
        client = await self.get_connection()
        return await client.zrem(key, *members)
    
    async def zremrangebyscore(self, key: str, min_score: float, max_score: float) -> int:
        """
        按分数范围删除有序集合成员
        
        Args:
            key: Redis键
            min_score: 最小分数
            max_score: 最大分数
            
        Returns:
            删除的成员数量
        """
        client = await self.get_connection()
        return await client.zremrangebyscore(key, min_score, max_score)
    
    async def zcard(self, key: str) -> int:
        """
        获取有序集合大小
        
        Args:
            key: Redis键
            
        Returns:
            集合大小
        """
        client = await self.get_connection()
        return await client.zcard(key)
    
    async def zcount(self, key: str, min_score: float, max_score: float) -> int:
        """
        统计分数范围内的成员数量
        
        Args:
            key: Redis键
            min_score: 最小分数
            max_score: 最大分数
            
        Returns:
            成员数量
        """
        client = await self.get_connection()
        return await client.zcount(key, min_score, max_score)
    
    async def zscore(self, key: str, member: str) -> Optional[float]:
        """
        获取成员分数
        
        Args:
            key: Redis键
            member: 成员
            
        Returns:
            成员分数，不存在时返回None
        """
        client = await self.get_connection()
        return await client.zscore(key, member)
    
    async def zincrby(self, key: str, amount: float, member: str) -> float:
        """
        增加成员分数
        
        Args:
            key: Redis键
            amount: 增加的分数
            member: 成员
            
        Returns:
            新的分数
        """
        client = await self.get_connection()
        return await client.zincrby(key, amount, member)
    
    async def zscan(self, key: str, cursor: int = 0, match: str = None, count: int = None) -> Tuple[int, List[Tuple[str, float]]]:
        """
        扫描有序集合
        
        Args:
            key: Redis键
            cursor: 游标位置
            match: 模式匹配
            count: 扫描数量
            
        Returns:
            (新游标, [(成员, 分数)])
        """
        client = await self.get_connection()
        return await client.zscan(key, cursor=cursor, match=match, count=count)
    
    async def pipeline(self, transaction: bool = True):
        """
        获取Redis管道
        
        Args:
            transaction: 是否使用事务（默认True）
        
        Returns:
            Redis管道对象
        """
        client = await self.get_connection()
        return client.pipeline(transaction=transaction)
    
    async def execute_pipeline(self, pipe) -> List[Any]:
        """
        执行管道
        
        Args:
            pipe: 管道对象
            
        Returns:
            执行结果列表
        """
        return await pipe.execute()
    
    # ==================== 批处理操作 ====================
    
    async def batch_zadd(self, operations: List[Tuple[str, Dict[str, float], bool, bool]]) -> List[int]:
        """
        批量添加有序集合成员（使用pipeline）
        
        Args:
            operations: 操作列表，每个元素为 (key, mapping, nx, xx)
                - key: Redis键
                - mapping: 成员和分数的映射 {member: score}
                - nx: 仅当成员不存在时添加
                - xx: 仅当成员存在时更新
            
        Returns:
            每个操作的结果列表
        """
        if not operations:
            return []
        
        pipe = await self.pipeline()
        for key, mapping, nx, xx in operations:
            pipe.zadd(key, mapping, nx=nx, xx=xx)
        
        return await self.execute_pipeline(pipe)
    
    async def batch_zincrby(self, operations: List[Tuple[str, float, str]]) -> List[float]:
        """
        批量增加成员分数（使用pipeline）
        
        Args:
            operations: 操作列表，每个元素为 (key, amount, member)
                - key: Redis键
                - amount: 增加的分数
                - member: 成员
            
        Returns:
            新分数列表
        """
        if not operations:
            return []
        
        pipe = await self.pipeline()
        for key, amount, member in operations:
            pipe.zincrby(key, amount, member)
        
        return await self.execute_pipeline(pipe)
    
    async def batch_zrem(self, operations: List[Tuple[str, List[str]]]) -> List[int]:
        """
        批量删除有序集合成员（使用pipeline）
        
        Args:
            operations: 操作列表，每个元素为 (key, members)
                - key: Redis键
                - members: 要删除的成员列表
            
        Returns:
            每个操作删除的成员数量列表
        """
        if not operations:
            return []
        
        pipe = await self.pipeline()
        for key, members in operations:
            pipe.zrem(key, *members)
        
        return await self.execute_pipeline(pipe)
    
    async def batch_zscore(self, queries: List[Tuple[str, str]]) -> List[Optional[float]]:
        """
        批量获取成员分数（使用pipeline）
        
        Args:
            queries: 查询列表，每个元素为 (key, member)
                - key: Redis键
                - member: 成员
            
        Returns:
            分数列表，不存在的成员返回None
        """
        if not queries:
            return []
        
        pipe = await self.pipeline()
        for key, member in queries:
            pipe.zscore(key, member)
        
        return await self.execute_pipeline(pipe)
    
    async def batch_zrangebyscore(self, queries: List[Tuple[str, float, float, Optional[int], Optional[int]]]) -> List[List[str]]:
        """
        批量按分数范围获取有序集合成员（使用pipeline）
        
        Args:
            queries: 查询列表，每个元素为 (key, min_score, max_score, start, num)
                - key: Redis键
                - min_score: 最小分数
                - max_score: 最大分数
                - start: 起始位置（可选）
                - num: 返回数量（可选）
            
        Returns:
            每个查询的成员列表
        """
        if not queries:
            return []
        
        pipe = await self.pipeline()
        for query in queries:
            key, min_score, max_score = query[:3]
            start = query[3] if len(query) > 3 else None
            num = query[4] if len(query) > 4 else None
            
            if start is None and num is None:
                pipe.zrangebyscore(key, min_score, max_score)
            else:
                pipe.zrangebyscore(key, min_score, max_score, start=start, num=num)
        
        return await self.execute_pipeline(pipe)
    
    # ==================== 工具方法 ====================
    
    async def exists(self, key: str) -> bool:
        """
        检查键是否存在
        
        Args:
            key: Redis键
            
        Returns:
            键是否存在
        """
        client = await self.get_connection()
        return await client.exists(key) > 0
    
    async def delete(self, *keys: str) -> int:
        """
        删除键
        
        Args:
            keys: 要删除的键
            
        Returns:
            删除的键数量
        """
        client = await self.get_connection()
        return await client.delete(*keys)
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """
        获取匹配模式的所有键
        
        Args:
            pattern: 模式
            
        Returns:
            键列表
        """
        client = await self.get_connection()
        return await client.keys(pattern)