"""
代理管理服务
组合storage和其他services，提供完整的代理管理功能
严格遵循：只依赖models和storage层
"""

import random
from typing import Dict, List, Optional, Any, Union

from yqproxy.utils import get_logger

# 只依赖models和storage层
from yqproxy.models import Proxy, Protocol, SUPPORTED_PROTOCOLS
from yqproxy.storage import ProxyPool, RedisClient
from yqproxy.models.constants import PROXY_SCORE_MAX

# 导入拆分的模块
from yqproxy.services.validator import ProxyValidator
from yqproxy.services.parser import ProxyParser
from yqproxy.services.statistics import ProxyStatistics


class ProxyManager:
    """
    代理管理服务
    组合storage层和其他service模块提供完整的代理管理功能
    通过依赖注入使用其他服务，避免循环依赖
    """
    
    def __init__(self,
                 redis_client: RedisClient,
                 key_prefix: str = "proxies",
                 protocol_detector = None,
                 proxy_tester = None):
        """
        初始化代理管理器
        
        Args:
            redis_client: Redis客户端
            key_prefix: Redis键前缀，默认为"proxies"
            protocol_detector: 协议检测器（可选，通过依赖注入）
            proxy_tester: 代理测试器（可选，通过依赖注入）
        """
        self.key_prefix = key_prefix
        self.proxy_pool = ProxyPool(redis_client, key_prefix)
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化子模块
        self.validator = ProxyValidator(self.proxy_pool, proxy_tester)
        self.parser = ProxyParser(protocol_detector)
        self.statistics = ProxyStatistics(self.proxy_pool)
        
        # 保存引用（向后兼容）
        self._protocol_detector = protocol_detector
        self._proxy_tester = proxy_tester
    
    def set_protocol_detector(self, detector):
        """设置协议检测器"""
        self._protocol_detector = detector
        self.parser.set_protocol_detector(detector)
    
    def set_proxy_tester(self, tester):
        """设置代理测试器"""
        self._proxy_tester = tester
        self.validator.set_proxy_tester(tester)
    
    # ==================== 代理添加管理 ====================
    
    async def add_proxy(self, proxy: Proxy) -> bool:
        """
        添加单个代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            是否添加成功
        """
        return await self.proxy_pool.add_proxy(proxy)
    
    async def add_proxy_from_string(self, proxy_string: str, 
                                   default_protocol: str = Protocol.HTTP) -> bool:
        """
        从字符串添加代理
        
        Args:
            proxy_string: 代理字符串
            default_protocol: 默认协议
            
        Returns:
            是否添加成功
        """
        proxy = self.parser.parse_proxy_string(proxy_string, default_protocol)
        if proxy:
            return await self.add_proxy(proxy)
        return False
    
    async def add_proxies_batch(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量添加代理
        
        Args:
            proxies: 代理列表
            
        Returns:
            各协议的添加结果 {protocol: count}
        """
        return await self.proxy_pool.add_proxies_batch(proxies)
    
    async def add_proxies_from_strings(self, proxy_strings: List[str],
                                      default_protocol: str = Protocol.HTTP) -> Dict[str, int]:
        """
        从字符串列表批量添加代理
        
        Args:
            proxy_strings: 代理字符串列表
            default_protocol: 默认协议
            
        Returns:
            各协议的添加结果 {protocol: count}
        """
        proxies = self.parser.parse_proxy_strings(proxy_strings, default_protocol)
        return await self.add_proxies_batch(proxies)
    
    # ==================== 代理获取管理 ====================
    
    async def get_random_proxy(self, protocol: str = None, n: int = 1) -> Union[Optional[Proxy], List[Proxy]]:
        """
        获取随机满分代理（支持单个和批量获取）
        
        重构说明：
        - 必须指定协议
        - 只返回满分代理（score = 100）
        - 当 n=1 时，返回单个 Proxy 对象或 None（向后兼容）
        - 当 n>1 时，返回 List[Proxy]（可能为空列表或少于 n 个）
        - 如果满分代理数量不足 n 个，返回所有可用的满分代理
        
        Args:
            protocol: 指定协议（必须提供）
            n: 获取数量，默认为 1
            
        Returns:
            - 当 n=1 时：满分代理对象或 None
            - 当 n>1 时：满分代理列表（可能少于 n 个）
        """
        # 参数验证
        if not protocol:
            self.logger.warning("获取随机代理必须指定协议")
            return None if n == 1 else []
        
        if protocol not in SUPPORTED_PROTOCOLS:
            self.logger.warning(f"不支持的协议类型: {protocol}")
            return None if n == 1 else []
        
        if n <= 0:
            self.logger.warning(f"无效的获取数量: {n}")
            return None if n == 1 else []
        
        # 获取所有满分代理
        key = self.proxy_pool._get_storage_key(protocol)
        try:
            # 仅获取满分代理（score = PROXY_SCORE_MAX）
            addresses = await self.proxy_pool.redis.zrangebyscore(
                key,
                PROXY_SCORE_MAX,
                PROXY_SCORE_MAX
            )
            
            if not addresses:
                self.logger.debug(f"协议 {protocol} 没有满分代理可用")
                return None if n == 1 else []
            
            # 处理单个代理请求（向后兼容）
            if n == 1:
                address = random.choice(addresses)
                proxy = Proxy.from_address(address, protocol=protocol)
                proxy.score = PROXY_SCORE_MAX
                self.logger.debug(f"获取到满分代理: {proxy.address} (协议: {protocol})")
                return proxy
            
            # 处理批量代理请求
            available_count = len(addresses)
            if available_count <= n:
                # 如果可用代理数量不足，返回所有可用代理
                proxies = []
                for address in addresses:
                    proxy = Proxy.from_address(address, protocol=protocol)
                    proxy.score = PROXY_SCORE_MAX
                    proxies.append(proxy)
                self.logger.debug(f"协议 {protocol} 可用满分代理数量 ({available_count}) 少于请求数量 ({n})，返回所有可用代理")
                return proxies
            else:
                # 随机选择 n 个代理
                selected_addresses = random.sample(addresses, n)
                proxies = []
                for address in selected_addresses:
                    proxy = Proxy.from_address(address, protocol=protocol)
                    proxy.score = PROXY_SCORE_MAX
                    proxies.append(proxy)
                self.logger.debug(f"从 {available_count} 个满分代理中随机选择了 {n} 个 (协议: {protocol})")
                return proxies
                
        except Exception as e:
            self.logger.error(f"获取满分代理失败 {protocol}: {e}")
            return None if n == 1 else []
    
    async def get_all_proxies(self, protocol: str = None) -> List[Proxy]:
        """
        获取所有代理
        
        Args:
            protocol: 协议类型（可选，不指定则获取所有协议）
            
        Returns:
            代理列表
        """
        return await self.proxy_pool.get_all_proxies(protocol)
    
    # ==================== 代理测试管理（委托给validator） ====================
    
    async def test_proxy(self, proxy: Proxy) -> bool:
        """
        测试单个代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        return await self.validator.test_proxy(proxy)
    
    async def test_and_update_proxy(self, proxy: Proxy) -> bool:
        """
        测试代理并更新分数
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        return await self.validator.test_and_update_proxy(proxy)
    
    async def test_proxies_batch(self, proxies: List[Proxy]) -> Dict[str, bool]:
        """
        批量测试代理
        
        Args:
            proxies: 代理列表
            
        Returns:
            测试结果 {address: success}
        """
        return await self.validator.test_proxies_batch(proxies)
    
    # ==================== 代理状态管理 ====================
    
    async def increase_proxy_score(self, proxy: Proxy, amount: int = 1) -> Optional[float]:
        """增加代理分数"""
        return await self.proxy_pool.increase_score(proxy, amount)
    
    async def decrease_proxy_score(self, proxy: Proxy, amount: int = 1) -> Optional[float]:
        """降低代理分数"""
        return await self.proxy_pool.decrease_score(proxy, amount)
    
    async def set_proxy_max_score(self, proxy: Proxy) -> bool:
        """设置代理为最高分数"""
        return await self.proxy_pool.set_max_score(proxy)
    
    async def remove_proxy(self, proxy: Proxy) -> bool:
        """删除代理"""
        return await self.proxy_pool.remove_proxy(proxy)
    
    async def cleanup_invalid_proxies(self) -> Dict[str, int]:
        """
        清理无效代理
        
        Returns:
            清理结果 {protocol: removed_count}
        """
        return await self.proxy_pool.cleanup_invalid_proxies()
    
    # ==================== 统计和维护（委托给statistics） ====================
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return await self.statistics.get_statistics()
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        return await self.statistics.health_check()
    
    async def get_score_distribution(self, protocol: str = None) -> Dict[str, Any]:
        """
        获取分数分布情况
        
        Args:
            protocol: 协议类型（可选）
            
        Returns:
            分数分布信息
        """
        return await self.statistics.get_score_distribution(protocol)
    
    async def generate_report(self) -> str:
        """
        生成统计报告
        
        Returns:
            格式化的统计报告
        """
        return await self.statistics.generate_report()
    
    # ==================== 生命周期管理 ====================
    
    async def close(self):
        """关闭管理器"""
        await self.proxy_pool.close()


# 便利函数已移除，直接使用 ProxyManager 类