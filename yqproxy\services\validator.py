"""
代理验证器模块
负责代理的测试和验证相关功能
"""

from typing import Dict, List
from yqproxy.utils import get_logger
from yqproxy.models import Proxy
from yqproxy.models.constants import PROXY_SCORE_DECREASE


class ProxyValidator:
    """
    代理验证器
    负责代理的测试、验证和分数更新
    """
    
    def __init__(self, proxy_pool, proxy_tester=None):
        """
        初始化代理验证器
        
        Args:
            proxy_pool: 代理池实例
            proxy_tester: 代理测试器（可选）
        """
        self.proxy_pool = proxy_pool
        self._proxy_tester = proxy_tester
        self.logger = get_logger(self.__class__.__name__)
    
    def set_proxy_tester(self, tester):
        """设置代理测试器"""
        self._proxy_tester = tester
    
    async def test_proxy(self, proxy: Proxy) -> bool:
        """
        测试单个代理
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        if not self._proxy_tester:
            self.logger.warning("代理测试器未设置，无法进行测试")
            return False
        
        try:
            result = await self._proxy_tester.test_single_proxy(proxy)
            return result.success
        except Exception as e:
            self.logger.error(f"代理测试失败: {proxy.address} -> {e}")
            return False
    
    async def test_and_update_proxy(self, proxy: Proxy) -> bool:
        """
        测试代理并更新分数
        
        Args:
            proxy: 代理对象
            
        Returns:
            测试是否成功
        """
        success = await self.test_proxy(proxy)
        
        if success:
            await self.proxy_pool.set_max_score(proxy)
            self.logger.debug(f"代理测试成功，设置满分: {proxy.address}")
        else:
            await self.proxy_pool.decrease_score(proxy, PROXY_SCORE_DECREASE)
            self.logger.debug(f"代理测试失败，降低分数: {proxy.address}")
        
        return success

    async def test_proxies_batch(self, proxies: List[Proxy]) -> Dict[str, bool]:
        """
        批量测试代理
        
        Args:
            proxies: 代理列表
            
        Returns:
            测试结果 {address: success}
        """
        if not self._proxy_tester:
            self.logger.warning("代理测试器未设置，无法进行测试")
            return {}
        
        try:
            results = await self._proxy_tester.test_proxy_batch(proxies)
            return {result.proxy.address: result.success for result in results}
        except Exception as e:
            self.logger.error(f"批量测试代理失败: {e}")
            return {}
    
    async def validate_and_score_batch(self, proxies: List[Proxy]) -> Dict[str, int]:
        """
        批量验证代理并更新分数
        
        Args:
            proxies: 代理列表
            
        Returns:
            验证结果统计 {status: count}
        """
        if not self._proxy_tester:
            self.logger.warning("代理测试器未设置，无法进行验证")
            return {'skipped': len(proxies)}
        
        success_count = 0
        failed_count = 0
        
        try:
            results = await self._proxy_tester.test_proxy_batch(proxies)
            
            for result in results:
                if result.success:
                    await self.proxy_pool.set_max_score(result.proxy)
                    success_count += 1
                else:
                    await self.proxy_pool.decrease_score(result.proxy, PROXY_SCORE_DECREASE)
                    failed_count += 1
            
            self.logger.info(f"批量验证完成: 成功 {success_count}, 失败 {failed_count}")
            
            return {
                'success': success_count,
                'failed': failed_count
            }
            
        except Exception as e:
            self.logger.error(f"批量验证代理失败: {e}")
            return {
                'error': len(proxies)
            }