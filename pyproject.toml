[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "yqproxy"
version = "0.1.0"
description = "动态http代理池"
readme = "README.md"
authors = [
    {name = "yyh357", email = "<EMAIL>"},
]
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = [
    "environs",
    "fastapi",
    "uvicorn[standard]",
    "attrs",
    "retrying",
    "aiohttp",
    "aiohttp-socks",
    "requests",
    "loguru",
    "redis",
    "fake_headers",
    "urllib3",
    "tqdm",  # 进度条显示
]

[tool.setuptools.packages.find]
where = ["."]
include = ["yqproxy*"]
exclude = ["tests*", "examples*", "logs*", "*.egg-info*", "__pycache__*", ".env*"]