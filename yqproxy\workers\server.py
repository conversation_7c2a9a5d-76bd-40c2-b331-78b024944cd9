#!/usr/bin/env python3
"""
API服务器工作进程
基于services层重构的API服务逻辑，独立可运行
"""

from contextlib import asynccontextmanager
from typing import Optional
from fastapi import FastAPI, HTTPException, Depends, Query, Path as PathParam
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import PlainTextResponse, JSONResponse

from yqproxy.services import ProxyManager, create_integrated_services
from yqproxy.models import SUPPORTED_PROTOCOLS
from yqproxy.utils import get_logger
from yqproxy.settings import API_HOST, API_PORT, API_KEY, DEBUG_MODE, API_BATCH_LIMIT


class ProxyAPIServer:
    """代理API服务器"""
    
    def __init__(self):
        self.proxy_manager: Optional[ProxyManager] = None
        self.app: Optional[FastAPI] = None
        self.logger = get_logger(__name__)
        
        # 从 setting 模块读取配置
        self.host = API_HOST
        self.port = API_PORT
        self.api_key = API_KEY
        self.debug_mode = DEBUG_MODE
        self.enable_cors = True  # 默认启用CORS
        
        # CORS配置（使用默认值，可根据需要在setting.py中添加配置）
        self.cors_origins = ['*']
        self.cors_methods = ['GET', 'POST', 'HEAD', 'OPTIONS']
        self.cors_headers = ['Content-Type', 'API-KEY', 'User-Agent', 'Accept', 'Authorization']
    
    async def initialize_services(self):
        """初始化服务依赖"""
        try:
            # 创建集成服务
            protocol_detector, proxy_tester, proxy_manager = await create_integrated_services()
            self.proxy_manager = proxy_manager
            
        except Exception as e:
            self.logger.error(f"API服务器服务依赖初始化失败: {e}")
            raise
    
    async def cleanup_services(self):
        """清理服务资源"""
        if self.proxy_manager:
            await self.proxy_manager.close()
    
    def create_app(self) -> FastAPI:
        """创建FastAPI应用"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            """应用生命周期管理"""
            # 启动时初始化
            await self.initialize_services()
            
            yield
            
            # 关闭时清理
            await self.cleanup_services()
        
        # 创建FastAPI应用
        app = FastAPI(
            title="协议感知代理池API",
            description="支持HTTP、SOCKS4、SOCKS5协议的高性能代理池API服务",
            version="4.0.0",
            lifespan=lifespan,
            debug=self.debug_mode
        )
        
        # 添加CORS中间件
        if self.enable_cors:
            app.add_middleware(
                CORSMiddleware,
                allow_origins=self.cors_origins,
                allow_credentials=False,
                allow_methods=self.cors_methods,
                allow_headers=self.cors_headers,
                expose_headers=["Content-Length", "Content-Type"],
                max_age=3600,
            )
        
        # 注册路由
        self._register_routes(app)
        
        return app
    
    def _register_routes(self, app: FastAPI):
        """注册所有路由"""
        
        # API认证依赖
        async def auth_dependency(api_key: Optional[str] = Query(None, alias="API-KEY")):
            """API认证依赖"""
            if not self.api_key:
                return True

            if api_key is None:
                raise HTTPException(status_code=400, detail="请在查询参数中提供API密钥")

            if api_key != self.api_key:
                raise HTTPException(status_code=403, detail="提供的API密钥无效")

            return True
        
        # 获取管理器依赖
        async def get_manager():
            """获取代理管理器依赖"""
            if self.proxy_manager is None:
                raise HTTPException(status_code=503, detail="代理管理器未初始化")
            return self.proxy_manager
        
        # 错误处理辅助函数
        def handle_error(e: Exception, operation: str):
            """统一错误处理"""
            self.logger.error(f"{operation}失败: {e}")
            raise HTTPException(status_code=500, detail="内部服务器错误")
        
        # ==================== 路由定义 ====================
        
        @app.get("/", response_class=JSONResponse)
        async def index(_: bool = Depends(auth_dependency)):
            """API首页 - 显示服务信息"""
            return {
                "service": "协议感知代理池API",
                "version": "4.0.0",
                "architecture": "服务层架构",
                "supported_protocols": SUPPORTED_PROTOCOLS,
                "endpoints": {
                    "random": "获取随机代理",
                    "protocol/{protocol}": "获取指定协议代理",
                    "batch": "批量获取代理",
                    "stats": "获取协议统计",
                    "health": "健康检查",
                    "cleanup": "清理无效代理"
                }
            }
        
        @app.get("/random", response_class=PlainTextResponse)
        async def get_random_proxy(
            protocol: Optional[str] = Query(None, description="指定协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取随机代理"""
            try:
                proxy = await manager.get_random_proxy(protocol)
                if not proxy:
                    raise HTTPException(status_code=404, detail="没有可用的代理")
                return proxy.get_normalized_string()
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, "获取随机代理")
        
        @app.get("/protocol/{protocol_name}", response_class=PlainTextResponse)
        async def get_proxy_by_protocol(
            protocol_name: str = PathParam(..., description="协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取指定协议的代理"""
            try:
                # 验证协议
                if protocol_name.lower() not in SUPPORTED_PROTOCOLS:
                    raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
                
                proxy = await manager.get_random_proxy(protocol_name.lower())
                if not proxy:
                    raise HTTPException(status_code=404, detail=f"没有可用的{protocol_name.upper()}代理")
                return proxy.get_normalized_string()
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, f"获取{protocol_name}代理")
        
        @app.get("/batch", response_class=PlainTextResponse)
        async def get_batch_proxies(
            count: int = Query(10, description=f"获取数量 (最大{API_BATCH_LIMIT})", ge=1, le=API_BATCH_LIMIT),
            protocol: Optional[str] = Query(None, description="指定协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """批量获取代理
            
            单次最多获取 {API_BATCH_LIMIT} 个代理，如需更多请分批请求。
            可通过环境变量 API_BATCH_LIMIT 调整此限制。
            """.format(API_BATCH_LIMIT=API_BATCH_LIMIT)
            try:
                # 验证协议
                if protocol and protocol.lower() not in SUPPORTED_PROTOCOLS:
                    raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol}")
                
                # 使用新的 get_random_proxy 方法批量获取
                if protocol:
                    proxies = await manager.get_random_proxy(protocol.lower(), n=count)
                else:
                    # 如果没有指定协议，从所有协议获取
                    all_proxies = []
                    remaining = count
                    for proto in SUPPORTED_PROTOCOLS:
                        if remaining <= 0:
                            break
                        proto_proxies = await manager.get_random_proxy(proto, n=remaining)
                        if isinstance(proto_proxies, list):
                            all_proxies.extend(proto_proxies)
                            remaining -= len(proto_proxies)
                    proxies = all_proxies[:count]
                
                if not proxies:
                    raise HTTPException(status_code=404, detail="没有可用的代理")
                
                # 确保 proxies 是列表
                if not isinstance(proxies, list):
                    proxies = [proxies] if proxies else []
                
                return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, "批量获取代理")
        
        @app.get("/all/{protocol_name}", response_class=PlainTextResponse)
        async def get_all_proxies_by_protocol(
            protocol_name: str = PathParam(..., description="协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取指定协议的所有代理"""
            try:
                # 验证协议
                if protocol_name.lower() not in SUPPORTED_PROTOCOLS:
                    raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
                
                # 获取大量代理（实际获取所有）
                proxies = await manager.get_random_proxy(protocol_name.lower(), n=10000)
                
                # 确保 proxies 是列表
                if not isinstance(proxies, list):
                    proxies = [proxies] if proxies else []
                
                if not proxies:
                    return ""
                
                return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, f"获取{protocol_name}所有代理")
        
        @app.get("/valid/{protocol_name}", response_class=PlainTextResponse)
        async def get_valid_proxies_by_protocol(
            protocol_name: str = PathParam(..., description="协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取指定协议的所有有效代理（高分代理）"""
            try:
                # 验证协议
                if protocol_name.lower() not in SUPPORTED_PROTOCOLS:
                    raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
                
                # 使用新的 get_random_proxy 方法获取满分代理
                proxies = await manager.get_random_proxy(protocol_name.lower(), n=10000)
                
                # 确保 proxies 是列表
                if not isinstance(proxies, list):
                    proxies = [proxies] if proxies else []
                
                if not proxies:
                    return ""
                
                return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, f"获取{protocol_name}有效代理")
        
        @app.get("/count", response_class=JSONResponse)
        async def get_proxy_counts(
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取各协议代理数量统计"""
            try:
                stats = await manager.get_statistics()
                total = stats.get('total_proxies', 0)
                protocols = {}
                
                for protocol, protocol_stats in stats.get('protocols', {}).items():
                    protocols[protocol] = protocol_stats.get('count', 0)
                
                return {
                    "total": total,
                    "protocols": protocols
                }
            except Exception as e:
                handle_error(e, "获取代理数量")
        
        @app.get("/count/{protocol_name}", response_class=PlainTextResponse)
        async def get_protocol_count(
            protocol_name: str = PathParam(..., description="协议类型"),
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取指定协议的代理数量"""
            try:
                # 验证协议
                if protocol_name.lower() not in SUPPORTED_PROTOCOLS:
                    raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
                
                stats = await manager.get_statistics()
                count = stats.get('protocols', {}).get(protocol_name.lower(), {}).get('count', 0)
                return str(count)
            except HTTPException:
                raise
            except Exception as e:
                handle_error(e, f"获取{protocol_name}代理数量")
        
        @app.get("/stats", response_class=JSONResponse)
        async def get_protocol_statistics(
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取详细的协议统计信息"""
            try:
                stats = await manager.get_statistics()
                return stats
            except Exception as e:
                handle_error(e, "获取协议统计")
        
        @app.get("/health", response_class=JSONResponse)
        async def health_check(
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """健康检查接口"""
            try:
                health = await manager.health_check()
                
                # 根据健康状态设置HTTP状态码
                status_code = 200
                if health.get('status') == 'warning':
                    status_code = 206  # Partial Content
                elif health.get('status') == 'error':
                    status_code = 503  # Service Unavailable
                
                return JSONResponse(content=health, status_code=status_code)
            except Exception as e:
                self.logger.error(f"健康检查失败: {e}")
                return JSONResponse(
                    content={"status": "error", "error": str(e)},
                    status_code=503
                )
        
        @app.post("/cleanup", response_class=JSONResponse)
        async def cleanup_invalid_proxies(
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """清理无效代理"""
            try:
                result = await manager.cleanup_invalid_proxies()
                total_cleaned = sum(result.values())
                return {
                    "message": f"清理完成，删除 {total_cleaned} 个无效代理",
                    "result": result
                }
            except Exception as e:
                handle_error(e, "清理无效代理")
        
        # 兼容性接口（保持与旧版本的兼容性）
        @app.get("/all", response_class=PlainTextResponse)
        async def get_all_proxies_legacy(
            _: bool = Depends(auth_dependency),
            manager: ProxyManager = Depends(get_manager)
        ):
            """获取所有代理（兼容性接口）"""
            try:
                # 从所有协议获取代理
                all_proxies = []
                for protocol in SUPPORTED_PROTOCOLS:
                    proxies = await manager.get_random_proxy(protocol, n=10000)
                    # 确保 proxies 是列表
                    if isinstance(proxies, list):
                        all_proxies.extend(proxies)
                    elif proxies:
                        all_proxies.append(proxies)
                
                if not all_proxies:
                    return ""
                
                return '\n'.join(proxy.get_normalized_string() for proxy in all_proxies)
            except Exception as e:
                handle_error(e, "获取所有代理")
    
    def get_app(self) -> FastAPI:
        """获取FastAPI应用实例"""
        if self.app is None:
            self.app = self.create_app()
        return self.app


# 全局服务器实例
_server_instance = None


def get_server() -> ProxyAPIServer:
    """获取全局服务器实例"""
    global _server_instance
    if _server_instance is None:
        _server_instance = ProxyAPIServer()
    return _server_instance


# 创建应用实例供外部导入
app = get_server().get_app()


def main():
    """主函数"""
    import uvicorn
    
    server = get_server()
    
    # 只显示服务器地址
    print(f'服务器地址: http://{server.host}:{server.port}')
    
    # 根据调试模式设置日志级别
    log_level = "warning"  # 减少日志输出
    
    if server.debug_mode:
        # 调试模式：启用自动重载
        uvicorn.run(
            "yqproxy.workers.server:app",
            host=server.host,
            port=server.port,
            reload=True,
            log_level=log_level,
            access_log=False  # 禁用访问日志
        )
    else:
        # 生产模式：单进程运行
        uvicorn.run(
            "yqproxy.workers.server:app",
            host=server.host,
            port=server.port,
            workers=1,
            log_level=log_level,
            access_log=False  # 禁用访问日志
        )


if __name__ == '__main__':
    main()